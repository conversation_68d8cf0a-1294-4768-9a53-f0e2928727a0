import { invoke } from "@tauri-apps/api/core";
import { listen } from '@tauri-apps/api/event';

// const tbxProduct = document.getElementById("product") as HTMLElement;
// const tbxFirmwareVersion = document.getElementById("firmware-version") as HTMLInputElement;
// const tbxSerialNumber = document.getElementById("serial-number") as HTMLInputElement;

console.log("ChargeDetailsView.ts 已載入");

let tbxProduct: HTMLElement | null;
let tbxFirmwareVersion: HTMLElement | null;
let tbxSerialNumber: HTMLElement | null;

// 全域變數記錄成功初始化的 product
let successfulProduct: number | null = null;

async function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const SwitchStatus = {
  Disable: 0,
  Enable: 1
} as const;

// 測試蜂鳴器功能
async function testBuzzer() {
  try {
    console.log("開始測試蜂鳴器...");

    // 檢查是否有成功的 product
    if (successfulProduct === null) {
      console.error("沒有成功初始化的 product，無法測試蜂鳴器");
      return;
    }

    // 使用記錄的 product 進行初始化
    if (await invoke("Initial", { product: successfulProduct })) {
      await delay(500);
      console.log(`使用 product 0x${successfulProduct.toString(16)} 測試蜂鳴器`);

      // 先啟用蜂鳴器
      const enableResult = await invoke<boolean>("Charger_SetBuzzer_Test", { switchStatus: SwitchStatus.Enable });
      console.log("啟用蜂鳴器結果:", enableResult);

      // 等待 1 秒
      await delay(1000);

      // 關閉蜂鳴器
      const disableResult = await invoke<boolean>("Charger_SetBuzzer_Test", { switchStatus: SwitchStatus.Disable });
      console.log("關閉蜂鳴器結果:", disableResult);

      if (enableResult && disableResult) {
        console.log("蜂鳴器測試完成");
      } else {
        console.error("蜂鳴器測試失敗");
      }
    } else {
      console.error(`使用 product 0x${successfulProduct.toString(16)} 初始化失敗`);
    }
  } catch (error) {
    console.error("測試蜂鳴器時發生錯誤:", error);
  }
}
// 測試 LED 功能
async function testLED() {
  try {
    console.log("開始測試 LED...");
      
      // 檢查是否有成功的 product
      if (successfulProduct === null) {
        console.error("沒有成功初始化的 product，無法測試 LED");
        return;
      }

      // 使用記錄的 product 進行初始化
      if (await invoke("Initial", { product: successfulProduct })) {
        await delay(500);
        console.log(`使用 product 0x${successfulProduct.toString(16)} 測試 LED`);

        for (let i = 0; i < 3; i++) {
          await delay(500);
          console.log("testLED i = ", i);
          // 先啟用 LED
          const enableResult = await invoke<boolean>("Charger_SetLED_Test", { switchStatus: SwitchStatus.Enable });
          console.log("啟用 LED 結果:", enableResult);

          // 等待 0.5 秒
          await delay(500);

          // 關閉 LED
          const disableResult = await invoke<boolean>("Charger_SetLED_Test", { switchStatus: SwitchStatus.Disable });
          console.log("關閉 LED 結果:", disableResult);

          if (enableResult && disableResult) {
            console.log("LED 測試完成");
          } else {
            console.error("LED 測試失敗");
          }
        }
      } else {
        console.error(`使用 product 0x${successfulProduct.toString(16)} 初始化失敗`);
      }
    

  } catch (error) {
    console.error("測試 LED 時發生錯誤:", error);
  }
}

async function Initial() {
  // 儀表板頁面的初始化邏輯
  tbxProduct = document.getElementById("product");
  tbxFirmwareVersion = document.getElementById("firmware-version");
  tbxSerialNumber = document.getElementById("serial-number");

  // 設置按鈕事件監聽器
  const buzzerBtn = document.getElementById("set-buzzer-btn");
  const ledBtn = document.getElementById("set-led-btn");

  if (buzzerBtn) {
    buzzerBtn.setAttribute("disabled", "true");
    buzzerBtn.addEventListener("click", async () => {
      // 禁用按鈕防止重複點擊
      buzzerBtn.setAttribute("disabled", "true");
      buzzerBtn.textContent = "Testing...";

      await testBuzzer();

      // 重新啟用按鈕
      buzzerBtn.removeAttribute("disabled");
      buzzerBtn.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12a3 3 0 106 0v-1a3 3 0 00-6 0v1z"></path>
        </svg>
        <span>Test Buzzer</span>
      `;
    });
  }

  if (ledBtn) {
    ledBtn.setAttribute("disabled", "true");
    ledBtn.addEventListener("click", async () => {
      // 禁用按鈕防止重複點擊
      ledBtn.setAttribute("disabled", "true");
      ledBtn.textContent = "Testing...";

      await testLED();

      // 重新啟用按鈕
      ledBtn.removeAttribute("disabled");
      ledBtn.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
        <span>Test LED</span>
      `;
    });
  }

  const vList = [0x0000FFFF, 0xFFFF0000, 0x03EB4736];
  for (const product of vList) {
    if (await invoke("Initial", { product })) {
      await delay(500);

      // 記錄成功的 product
      successfulProduct = product;
      console.log(`成功初始化的 product: 0x${product.toString(16)}`);

      tbxProduct!.textContent = await invoke("Charger_GetName");
      tbxFirmwareVersion!.textContent = await invoke("Charger_GetVersion");
      tbxSerialNumber!.textContent = await invoke("Charger_GetSerialNumber");
      //await invoke("TEST");
      const buzzerBtn = document.getElementById("set-buzzer-btn");
      const ledBtn = document.getElementById("set-led-btn");
      if (ledBtn) {
        ledBtn.removeAttribute("disabled");
      }
      if (buzzerBtn) {
        buzzerBtn.removeAttribute("disabled");
      }
      return;
    }
  }

  // 如果所有 product 都失敗，清空記錄
  successfulProduct = null;
  tbxProduct!.textContent = "NA";
  tbxFirmwareVersion!.textContent = "NA";
  tbxSerialNumber!.textContent = "NA";
}

export async function upgrade(){
  try {
    if (await invoke("LOAD_SDK")) {
      listen("USB_ChangedEvent", async () => {
        Initial();
      });
      listen<number>('Upgrade_ProgressUpdatedEvent', (event) => {
        tbxProduct!.textContent = event.payload.toString() + "%";
      });
      Initial();
    }
  }
  catch (error) {
    console.error("Failed to load library:", error);
  }
}

// (async function() {
  
// })();
