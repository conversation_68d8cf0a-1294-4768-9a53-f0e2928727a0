use libloading::Library;
use libloading::Symbol;
use num_enum::TryFromPrimitive;
use once_cell::sync::Lazy;
use std::convert::TryFrom;
use std::ffi::CStr;
use std::ffi::CString;
use std::os::raw::c_char;
use std::path::PathBuf;
use std::sync::Arc;
use std::sync::Mutex;
use tauri::AppHandle;
use tauri::Emitter;
use serde::{Serialize, Deserialize};
use serde::ser::{Serializer}; // SerializeStruct
use rand::Rng;
use std::time::{SystemTime, UNIX_EPOCH};

#[repr(u8)]
#[derive(Debug, TryFromPrimitive, Clone, Copy, PartialEq, Eq)]
pub enum ConnectionStatus {
    Detached = 0x00,
    Attached = 0x01,
}

#[repr(u8)]
#[derive(Debug, TryFromPrimitive, Clone, Copy, PartialEq, Eq)]
pub enum LockStatus {
    Normal = 0x00,
    Lock = 0x01,
    Unlock = 0x02,
    None = 0x03,
}

#[repr(u8)]
#[derive(Debug, TryFromPrimitive, Clone, Copy, PartialEq, Eq)]
pub enum SwitchStatus 
{
    Disable = 0x00,
    Enable = 0x01,
}

#[repr(C)]
#[derive(Debug, Clone, Copy)]
pub struct Spec {
    pub Product: u32,
    pub Capabilities: u64,
    pub SlotCount: u8,
}

// 與 C++ 的 BatteryDataC 對應
#[repr(C)]
#[derive(Debug)]
pub struct BatteryDataC {
    pub ID: u16,
    pub SN: *const c_char,  // 用來對應 C 的 const char*
    pub Pack: u32,
    pub Mode: u16,
    pub DesignVoltage: u16,
    pub DesignCapacity: u16,
    pub RemainingCapacity: u16,
    pub FullyChargedCapacity: u16,
    pub RelativeStateOfCharged: u8,
    pub AbsoluteStateOfCharged: u8,
    pub UntilFullyCharged: u16,
    pub UntilFullyDischarged: u16,
    pub Cycle: u16,
    pub Voltage: u16,
    pub Current: i16,
    pub ChargingVoltage: u16,
    pub ChargingCurrent: u16,
    pub AverageCurrent: i16,
    pub Temperature: f64,
    pub ManufactureDate: *const c_char,
    pub GaugeStatus: u16,
    pub Error: u16,
    /* Extended Data (XXL) */
    pub CycleIndex: u16,
    pub CycleThreshold: u16,
    pub FullyChargedDate: *const c_char,
    pub FullyChargedCapacityThreshold: u16,
    pub FullyChargedCapacityBackup: u16,
    pub XXLLifetimeMaxPackVoltage: u16,
    pub XXLLifetimeMinPackVoltage: u16,
    pub XXLLifetimeMaxCurrent: i16,
    pub XXLLifetimeMinCurrent: i16,
    pub OrangeLED: u16,
    pub FullyChargedVoltage: u16,
    pub FirstUseDate: *const c_char,
    pub RecordDate: *const c_char,
    pub RecordTime: *const c_char,
    pub PackMode: u16,
    pub DiagParamPfFlag: u16,
    /* Extended Data */
    pub CellVoltage_1: u16,
    pub CellVoltage_2: u16,
    pub CellVoltage_3: u16,
    pub CellVoltage_4: u16,
    pub PackVoltage: u16,
    pub FETControl: u16,
    pub SafetyAlert_1: u16,
    pub SafetyAlert_2: u16,
    pub SafetyStatus_1: u16,
    pub SafetyStatus_2: u16,
    pub PFAlert_1: u16,
    pub PFAlert_2: u16,
    pub PFStatus_1: u16,
    pub PFStatus_2: u16,
    pub OperationStatus: u16,
    pub ChargingStatus: u16,
    pub TemperatureRange: u16,
    pub MaxError: u16,
    pub DeviceName: *const c_char,
    //pub MaxCellVoltage: u16,
    //pub MinCellVoltage: u16,
    /* Manufacture Block */
    pub ManufactureBlock_1: *const c_char,
    pub ManufactureBlock_2: *const c_char,
    pub ManufactureBlock_3: *const c_char,
    pub ManufactureBlock_4: *const c_char,
    /* Lifetime Data */
    pub LifetimeMaxTemperature: f64,
    pub LifetimeMinTemperature: f64,
    pub LifetimeAvgTemperature: f64,
    pub LifetimeMaxCellVoltage: u32,
    pub LifetimeMinCellVoltage: u32,
    pub LifetimeMaxPackVoltage: u32,
    pub LifetimeMinPackVoltage: u32,
    pub LifetimeMaxChargingCurrent: i32,
    pub LifetimeMaxDischargingCurrent: i32,
    pub LifetimeMaxAvgDischargingCurrent: i32,
    pub LifetimeMaxChargingPower: i32,
    pub LifetimeMaxDischargingPower: i32,
    pub LifetimeMaxAvgDischargingPower: i32,
    pub LifetimeTemperatureSamples: u32,
    pub OTEventCount: u32,
    pub OTEventDuration: u32,
    pub OVEventCount: u32,
    pub OVEventDuration: u32,
}
// C 結構無法再 Rust中實現 Serialize trait，
// 因為 C 結構中的指針無法直接序列化為 JSON 字符串。
impl Serialize for BatteryDataC {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        use serde::ser::SerializeStruct;
        let mut state = serializer.serialize_struct("BatteryDataC", 2)?;
        state.serialize_field("id", &self.ID)?;

        let sn_str = c_str2String(self.SN);
        state.serialize_field("sn", &sn_str)?;

        state.serialize_field("pack", &self.Pack)?;
        state.serialize_field("mode", &self.Mode)?;
        state.serialize_field("designVoltage", &self.DesignVoltage)?;
        state.serialize_field("designCapacity", &self.DesignCapacity)?;
        state.serialize_field("remainingCapacity", &self.RemainingCapacity)?;
        state.serialize_field("fullyChargedCapacity", &self.FullyChargedCapacity)?;
        state.serialize_field("relativeStateOfCharged", &self.RelativeStateOfCharged)?;
        state.serialize_field("absoluteStateOfCharged", &self.AbsoluteStateOfCharged)?;
        state.serialize_field("untilFullyCharged", &self.UntilFullyCharged)?;
        state.serialize_field("untilFullyDischarged", &self.UntilFullyDischarged)?;
        state.serialize_field("cycle", &self.Cycle)?;
        state.serialize_field("voltage", &self.Voltage)?;
        state.serialize_field("current", &self.Current)?;
        state.serialize_field("chargingVoltage", &self.ChargingVoltage)?;
        state.serialize_field("chargingCurrent", &self.ChargingCurrent)?;
        state.serialize_field("averageCurrent", &self.AverageCurrent)?;
        state.serialize_field("temperature", &self.Temperature)?;
        
        let manufactureDate_str = c_str2String(self.ManufactureDate);
        state.serialize_field("manufactureDate", &manufactureDate_str)?;
        state.serialize_field("gaugeStatus", &self.GaugeStatus)?;
        state.serialize_field("error", &self.Error)?;
        
        //// Extended Data (XXL)
        state.serialize_field("cycleIndex", &self.CycleIndex)?;
        state.serialize_field("cycleThreshold", &self.CycleThreshold)?;
        
        let fullyChargedDate_str = c_str2String(self.FullyChargedDate);
        state.serialize_field("fullyChargedDate", &fullyChargedDate_str)?;

        state.serialize_field("fullyChargedCapacityThreshold", &self.FullyChargedCapacityThreshold)?;
        state.serialize_field("fullyChargedCapacityBackup", &self.FullyChargedCapacityBackup)?;
        state.serialize_field("xxlLifetimeMaxPackVoltage", &self.XXLLifetimeMaxPackVoltage)?;
        state.serialize_field("xxlLifetimeMinPackVoltage", &self.XXLLifetimeMinPackVoltage)?;
        state.serialize_field("xxlLifetimeMaxCurrent", &self.XXLLifetimeMaxCurrent)?;
        state.serialize_field("xxlLifetimeMinCurrent", &self.XXLLifetimeMinCurrent)?;
        state.serialize_field("orangeLED", &self.OrangeLED)?;
        state.serialize_field("fullyChargedVoltage", &self.FullyChargedVoltage)?;
        
        let firstUseDate_str = c_str2String(self.FirstUseDate);
        state.serialize_field("firstUseDate", &firstUseDate_str)?;
        let recordDate_str = c_str2String(self.RecordDate);
        state.serialize_field("recordDate", &recordDate_str)?;
        let recordTime_str = c_str2String(self.RecordTime);
        state.serialize_field("recordTime", &recordTime_str)?;

        state.serialize_field("packMode", &self.PackMode)?;
        let diag_param_pf_flag = (self.SafetyStatus_1 & 0x20) > 0;
        state.serialize_field("diagParamPfFlag", &diag_param_pf_flag)?;

        //// Extended Data
        state.serialize_field("cellVoltage_1", &self.CellVoltage_1)?;
        state.serialize_field("cellVoltage_2", &self.CellVoltage_2)?;
        state.serialize_field("cellVoltage_3", &self.CellVoltage_3)?;
        state.serialize_field("cellVoltage_4", &self.CellVoltage_4)?;
        state.serialize_field("packVoltage", &self.PackVoltage)?;
        state.serialize_field("fetControl", &self.FETControl)?;
        state.serialize_field("safetyAlert_1", &self.SafetyAlert_1)?;
        state.serialize_field("safetyAlert_2", &self.SafetyAlert_2)?;
        state.serialize_field("safetyStatus_1", &self.SafetyStatus_1)?;
        state.serialize_field("safetyStatus_2", &self.SafetyStatus_2)?;
        state.serialize_field("pfAlert_1", &self.PFAlert_1)?;
        state.serialize_field("pfAlert_2", &self.PFAlert_2)?;
        state.serialize_field("pfStatus_1", &self.PFStatus_1)?;
        state.serialize_field("pfStatus_2", &self.PFStatus_2)?;
        state.serialize_field("operationStatus", &self.OperationStatus)?;
        state.serialize_field("chargingStatus", &self.ChargingStatus)?;
        state.serialize_field("temperatureRange", &self.TemperatureRange)?;
        state.serialize_field("maxError", &self.MaxError)?;

        // //let voltages = [&self.CellVoltage_1, &self.CellVoltage_2, &self.CellVoltage_3, &self.CellVoltage_4];
        // //let voltages = vec![&self.CellVoltage_1, &self.CellVoltage_2, &self.CellVoltage_3, &self.CellVoltage_4];
        // let mut cell_voltage_max = 0;
        // let mut cell_voltage_min = 0;
        // let mut voltages: Vec<u16> = Vec::new();
        // if self.CellVoltage_1 > 0 {
        //     voltages.push(self.CellVoltage_1);
        // }
        // if self.CellVoltage_2 > 0 {
        //     voltages.push(self.CellVoltage_2);
        // }
        // if self.CellVoltage_3 > 0 {
        //     voltages.push(self.CellVoltage_3);
        // }
        // if self.CellVoltage_4 > 0 {
        //     voltages.push(self.CellVoltage_4);
        // }
        // if voltages.is_empty() {
        //     // 如果沒有有效的電池電壓，則設置為 0
        // }
        // else{
        //     cell_voltage_max = *voltages.iter().max().unwrap();
        //     cell_voltage_min = *voltages.iter().min().unwrap();
        // }
        

        //state.serialize_field("maxCellVoltage", &cell_voltage_max)?;
        //state.serialize_field("minCellVoltage", &cell_voltage_min)?;
        // state.serialize_field("maxCellVoltage", &self.MaxCellVoltage)?;
        // state.serialize_field("minCellVoltage", &self.MinCellVoltage)?;

        // println!("&self.CellVoltage_1: {}", &self.CellVoltage_1);
        // println!("&self.CellVoltage_2: {}", &self.CellVoltage_2);
        // println!("&self.CellVoltage_3: {}", &self.CellVoltage_3);
        // println!("&self.CellVoltage_4: {}", &self.CellVoltage_4);
        // println!("cell_voltage_max: {}",  cell_voltage_max);
        // println!("cell_voltage_min: {}", cell_voltage_min);
        
        let deviceName_str = c_str2String(self.DeviceName);
        state.serialize_field("deviceName", &deviceName_str)?;

        //// Manufacture Block
        let manufactureBlock_1_str = c_str2String(self.ManufactureBlock_1);
        state.serialize_field("manufactureBlock_1", &manufactureBlock_1_str)?;
        let manufactureBlock_2_str = c_str2String(self.ManufactureBlock_2);
        state.serialize_field("manufactureBlock_2", &manufactureBlock_2_str)?;
        let manufactureBlock_3_str = c_str2String(self.ManufactureBlock_3);
        state.serialize_field("manufactureBlock_3", &manufactureBlock_3_str)?;
        let manufactureBlock_4_str = c_str2String(self.ManufactureBlock_4);
        state.serialize_field("manufactureBlock_4", &manufactureBlock_4_str)?;
        // state.serialize_field("manufactureBlock_1", &self.ManufactureBlock_1)?;
        // state.serialize_field("manufactureBlock_2", &self.ManufactureBlock_2)?;
        // state.serialize_field("manufactureBlock_3", &self.ManufactureBlock_3)?;
        // state.serialize_field("manufactureBlock_4", &self.ManufactureBlock_4)?;

        //// Lifetime Data
        state.serialize_field("lifetimeMaxTemperature", &self.LifetimeMaxTemperature)?;
        state.serialize_field("lifetimeMinTemperature", &self.LifetimeMinTemperature)?;
        state.serialize_field("lifetimeAvgTemperature", &self.LifetimeAvgTemperature)?;
        state.serialize_field("lifetimeMaxCellVoltage", &self.LifetimeMaxCellVoltage)?;
        state.serialize_field("lifetimeMinCellVoltage", &self.LifetimeMinCellVoltage)?;
        state.serialize_field("lifetimeMaxPackVoltage", &self.LifetimeMaxPackVoltage)?;
        state.serialize_field("lifetimeMinPackVoltage", &self.LifetimeMinPackVoltage)?;
        state.serialize_field("lifetimeMaxChargingCurrent", &self.LifetimeMaxChargingCurrent)?;
        state.serialize_field("lifetimeMaxDischargingCurrent", &self.LifetimeMaxDischargingCurrent)?;
        state.serialize_field("lifetimeMaxAvgDischargingCurrent", &self.LifetimeMaxAvgDischargingCurrent)?;
        state.serialize_field("lifetimeMaxChargingPower", &self.LifetimeMaxChargingPower)?;
        state.serialize_field("lifetimeMaxDischargingPower", &self.LifetimeMaxDischargingPower)?;
        state.serialize_field("lifetimeMaxAvgDischargingPower", &self.LifetimeMaxAvgDischargingPower)?;
        state.serialize_field("lifetimeTemperatureSamples", &self.LifetimeTemperatureSamples)?;
        state.serialize_field("otEventCount", &self.OTEventCount)?;
        state.serialize_field("otEventDuration", &self.OTEventDuration)?;
        state.serialize_field("ovEventCount", &self.OVEventCount)?;
        state.serialize_field("ovEventDuration", &self.OVEventDuration)?;
        // 完成序列化
        state.end()
    }
}


lazy_static::lazy_static! {
    // static ref ONYX_API: Mutex<Option<Library>> = Mutex::new(None);
    static ref ONYX_API: std::sync::Mutex<Option<Library>> = std::sync::Mutex::new(None);
    // static ref HIDAPI: std::sync::Mutex<Option<Library>> = std::sync::Mutex::new(None);
}

lazy_static::lazy_static! {
    static ref m_spec: Mutex<Spec> = Mutex::new(Spec {
        Product: 0,
        Capabilities: 0,
        SlotCount: 0,
    });
}

// 可序列化的電池數據結構體（用於模擬）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimulatedBatteryData {
    pub id: u16,
    pub sn: String,
    pub pack: u32,
    pub mode: u16,
    pub design_voltage: u16,
    pub design_capacity: u16,
    pub remaining_capacity: u16,
    pub fully_charged_capacity: u16,
    pub relative_state_of_charged: u8,
    pub absolute_state_of_charged: u8,
    pub until_fully_charged: u16,
    pub until_fully_discharged: u16,
    pub cycle: u16,
    pub voltage: u16,
    pub current: i16,
    pub charging_voltage: u16,
    pub charging_current: u16,
    pub average_current: i16,
    pub temperature: f64,
    pub manufacture_date: String,
    pub gauge_status: u16,
    pub error: u16,
    pub cycle_index: u16,
    pub cycle_threshold: u16,
    pub fully_charged_date: String,
    pub fully_charged_capacity_threshold: u16,
    pub fully_charged_capacity_backup: u16,
    pub xxl_lifetime_max_pack_voltage: u16,
    pub xxl_lifetime_min_pack_voltage: u16,
    pub xxl_lifetime_max_current: i16,
    pub xxl_lifetime_min_current: i16,
    pub orange_led: u16,
    pub fully_charged_voltage: u16,
    pub first_use_date: String,
    pub record_date: String,
    pub record_time: String,
    pub pack_mode: u16,
    pub cell_voltage_1: u16,
    pub cell_voltage_2: u16,
    pub cell_voltage_3: u16,
    pub cell_voltage_4: u16,
    pub pack_voltage: u16,
    pub fet_control: u16,
    pub safety_alert_1: u16,
    pub safety_alert_2: u16,
    pub safety_status_1: u16,
    pub safety_status_2: u16,
    pub pf_alert_1: u16,
    pub pf_alert_2: u16,
    pub pf_status_1: u16,
    pub pf_status_2: u16,
    pub operation_status: u16,
    pub charging_status: u16,
    pub temperature_range: u16,
    pub max_error: u16,
    pub max_cell_voltage: u16,
    pub min_cell_voltage: u16,
    pub device_name: String,
    pub manufacture_block_1: String,
    pub manufacture_block_2: String,
    pub manufacture_block_3: String,
    pub manufacture_block_4: String,
    pub lifetime_max_temperature: f64,
    pub lifetime_min_temperature: f64,
    pub lifetime_avg_temperature: f64,
    pub lifetime_max_cell_voltage: u32,
    pub lifetime_min_cell_voltage: u32,
    pub lifetime_max_pack_voltage: u32,
    pub lifetime_min_pack_voltage: u32,
    pub lifetime_max_charging_current: i32,
    pub lifetime_max_discharging_current: i32,
    pub lifetime_max_avg_discharging_current: i32,
    pub lifetime_max_charging_power: i32,
    pub lifetime_max_discharging_power: i32,
    pub lifetime_max_avg_discharging_power: i32,
    pub lifetime_temperature_samples: u32,
    pub ot_event_count: u32,
    pub ot_event_duration: u32,
    pub ov_event_count: u32,
    pub ov_event_duration: u32,
}

// 模擬模式相關的全局變數
lazy_static::lazy_static! {
    static ref SIMULATION_MODE: Mutex<bool> = Mutex::new(false);
    static ref SIMULATED_BATTERIES: Mutex<Vec<Option<SimulatedBatteryData>>> = Mutex::new(vec![None; 6]);
    static ref LAST_SIMULATION_UPDATE: Mutex<u64> = Mutex::new(0);
    // 存儲模擬字符串的全局變數，確保指針有效
    static ref SIMULATED_STRINGS: Mutex<Vec<CString>> = Mutex::new(Vec::new());
}

// 創建模擬字符串並返回指針
fn create_simulated_string(content: &str) -> *const c_char {
    let c_string = CString::new(content).unwrap_or_else(|_| CString::new("").unwrap());
    let ptr = c_string.as_ptr();
    
    // 將字符串存儲到全局變數中，確保指針有效
    let mut strings = SIMULATED_STRINGS.lock().unwrap();
    strings.push(c_string);
    
    ptr
}

#[tauri::command]
pub fn LOAD_SDK() -> Result<bool, String> {
    #[cfg(target_os = "windows")]{
        // 先載入依賴 DLL（但不存儲引用，讓它保持在記憶體中）
        let hidapi_path = GetHidApiLibraryPath();
        println!("預載入 hidapi SDK: {:?}", hidapi_path);

        if !hidapi_path.exists() {
            let err_msg = format!("載入 hidapi.dll 失敗: 檔案不存在: {:?}", hidapi_path);
            println!("{}", err_msg);
            return Err(err_msg);
        }

        unsafe {
            match Library::new(&hidapi_path) {
                Ok(_lib) => {
                    println!("預載入 hidapi SDK 成功...");
                    // 不存儲 _lib，但它會保持載入狀態
                    std::mem::forget(_lib); // 防止 DLL 被卸載
                }
                Err(e) => {
                    let err_msg = format!("預載入 hidapi SDK 失敗: {}", e);
                    println!("{}", err_msg);
                    return Err(err_msg);
                }
            }
        }
    }
    
    // 載入主要 DLL
    let mut api = ONYX_API.lock().unwrap();
    let upower_path = GetLibraryPath();
    println!("載入 UPower SDK: {:?}", upower_path);

    #[cfg(target_os = "windows")]
    {
        // Windows: 檢查檔案是否存在
        if !upower_path.exists() {
            let err_msg = format!("載入 UPower SDK 失敗: 檔案不存在於 {:?}", upower_path);
            println!("{}", err_msg);
            return Err(err_msg);
        }
    }

    unsafe {
        match Library::new(&upower_path) {
            Ok(lib) => {
                println!("載入 UPower SDK 成功...");
                *api = Some(lib);
                Ok(true)
            }
            Err(e) => {
                let err_msg = format!("載入 UPower SDK 失敗: {}", e);
                println!("{}", err_msg);
                Err(err_msg)
            }
        }
    }
}


fn GetLibraryPath() -> PathBuf {
    #[cfg(target_os = "linux")]
    {
        return PathBuf::from("libUPower-SDK.so");
    }
    #[cfg(target_os = "windows")] 
    {
        return PathBuf::from("src/main/assets/UPower-SDK.dll");
        // let current_dir = std::env::current_dir().unwrap();
        // return current_dir.join("src/main/assets/UPower-SDK.dll");
    }
}
fn GetHidApiLibraryPath() -> PathBuf {
    #[cfg(target_os = "windows")]
    {
        // return PathBuf::from("src/main/assets/hidapi.dll");
        let current_dir = std::env::current_dir().unwrap();
        return current_dir.join("src/main/assets/hidapi.dll");
    }
}

#[tauri::command]
pub fn TEST() {
    let member_spec = m_spec.lock().unwrap();
    println!("{:?}", member_spec);
    println!("Charger_GetBoardID: {}", Charger_GetBoardID());
    match ConnectionStatus::try_from(Charger_GetAdapterStatus()) {
        Ok(status) => println!("Charger_GetAdapterStatus: {:?}", status),
        Err(_) => println!("Charger_GetAdapterStatus: Err(_)"),
    }

    Charger_SetSlotStatus(0, LockStatus::Normal as u8);
    Charger_SetSlotStatus(1, LockStatus::Lock as u8);
    Charger_SetSlotStatus(2, LockStatus::Normal as u8);
    Charger_SetSlotStatus(3, LockStatus::Normal as u8);
    Charger_SetSlotStatus(4, LockStatus::Unlock as u8);
    Charger_SetSlotStatus(5, LockStatus::Normal as u8);

    for slot_index in 0..member_spec.SlotCount {
        match LockStatus::try_from(Charger_GetSlotStatus(slot_index)) {
            Ok(status) => println!("Charger_GetSlotStatus({}): {:?}", slot_index, status),
            Err(_) => println!("Charger_GetSlotStatus({}): Err(_)", slot_index),
        }
    }
    println!("Charger_GetErrorCode: {}", Charger_GetErrorCode());

    

    /*
    println!(
        "Charger_SetSerialNumber: {}",
        Charger_SetSerialNumber("H987654321-00000001".to_string())
    );
     */

    // serial 字串
    let serial = "H987654321-12300000";

    // 建立 C 字串（會自動加上 '\0'）
    let c_serial = CString::new(serial).expect("CString 建立失敗");

    println!(
        "Charger_SetSerialNumber: {}",
        Charger_SetSerialNumber(c_serial.as_ptr())
    );

    tauri::async_runtime::spawn(async move {
        SetProgressUpdated("Upgrade_ProgressUpdatedEvent".to_string());
        Upgrade_SetProgressUpdated();
        Upgrade_SetUpgrade("D:/Workspace/UPower62_v1.00.06_app.bin".to_string(), false);
    });

    // Charger_SetReboot();
}

#[tauri::command]
pub fn TestLed() {
    println!("Test LED");
    Charger_SetLED(SwitchStatus::Enable as u8);
    std::thread::sleep(std::time::Duration::from_secs(1));
    Charger_SetLED(SwitchStatus::Disable as u8);
}

#[tauri::command]
pub fn TestBuzzer() {
    println!("Test Buzzer");
    Charger_SetBuzzer(SwitchStatus::Enable as u8);
    std::thread::sleep(std::time::Duration::from_secs(1));
    Charger_SetBuzzer(SwitchStatus::Disable as u8);
}

// Tauri 命令包裝函數，用於直接控制 LED
#[tauri::command]
pub fn Charger_SetLED_Command(switch_status: u8) -> bool {
    println!("Charger_SetLED_Command: switch_status = {}", switch_status);
    Charger_SetLED(switch_status)
}

// Tauri 命令包裝函數，用於直接控制蜂鳴器
#[tauri::command]
pub fn Charger_SetBuzzer_Command(switch_status: u8) -> bool {
    println!("Charger_SetBuzzer_Command: switch_status = {}", switch_status);
    Charger_SetBuzzer(switch_status)
}


pub trait IRet {
    type RawType;
    unsafe fn from_raw(raw: Self::RawType) -> Self;
    unsafe fn default_value() -> Self;
}

impl IRet for String {
    type RawType = *const i8;
    unsafe fn from_raw(raw: Self::RawType) -> Self {
        CStr::from_ptr(raw).to_string_lossy().into_owned()
    }
    unsafe fn default_value() -> Self {
        "N/A".to_string()
    }
}

impl IRet for u8 {
    type RawType = u8;
    unsafe fn from_raw(raw: Self::RawType) -> Self {
        raw
    }
    unsafe fn default_value() -> Self {
        0x00
    }
}

impl IRet for *const c_char {
    type RawType = *const c_char;
    unsafe fn from_raw(raw: Self::RawType) -> Self {
        raw
    }
    unsafe fn default_value() -> Self {
        std::ptr::null()
    }
}

impl IRet for bool {
    type RawType = u8;
    unsafe fn from_raw(raw: Self::RawType) -> Self {
        raw != 0
    }
    unsafe fn default_value() -> Self {
        false
    }
}

macro_rules! C_Ty {
    ($c_ty:ty) => {
        <$c_ty as IRet>::RawType
    };
}

macro_rules! C_Symbol {
    ($ret_ty:ty, $($arg_ty:ty),*) => {
        Symbol<unsafe extern "C" fn($(C_Ty!($arg_ty),)*) -> C_Ty!($ret_ty)>
    };
}

macro_rules! ONYX_API {
    ($ret_ty:ty, $fn_name:ident, $($arg:ident : $arg_ty:ty),*) => {
        #[tauri::command]
        pub fn $fn_name($($arg: $arg_ty),*) -> $ret_ty {
            unsafe {
                let api = ONYX_API.lock().unwrap();
                let symbol: Result<C_Symbol!($ret_ty, $($arg_ty),*), _> = api.as_ref().unwrap().get(stringify!($fn_name).as_bytes());
                
                match symbol {
                    Ok(c_fn) => <$ret_ty as IRet>::from_raw(c_fn($($arg),*)),
                    Err(_) => <$ret_ty as IRet>::default_value(),
                }
            }
        }
    };
}

// 將 *const c_char 轉成 Rust 的 &CStr 再轉成 &str/String
pub fn c_str2String(ptr: *const c_char) -> String {
    if ptr.is_null() {
        return String::new();
    }
    unsafe {
        CStr::from_ptr(ptr)
            .to_string_lossy()
            .into_owned()
    }
}


#[tauri::command]
pub fn Initial(product: u32) -> bool {
    set_simulation_mode(false);
    // 檢查是否為模擬模式
    let is_simulation = is_simulation_mode();
    
    if is_simulation {
        println!("模擬模式：Initial 成功");
        // 在模擬模式下，設置一個模擬的 spec
        let mut member_spec = m_spec.lock().unwrap();
        *member_spec = Spec {
            Product: product,
            Capabilities: 0x12345678, // 模擬的能力值
            SlotCount: 6, // 模擬 6 個插槽
        };
        println!("模擬模式 spec: {:?}", *member_spec);
        return true;
    }
    
    // 原始的真實設備模式
    unsafe {
        let api = ONYX_API.lock().unwrap();
        if api.is_none() {
            println!("API 尚未載入，無法初始化");
            return false;
        }
        
        let c_fn: Symbol<unsafe extern "C" fn(*mut Spec) -> bool> =
            match api.as_ref().unwrap().get(b"Initial") {
                Ok(f) => f,
                Err(_) => {
                    println!("無法載入 Initial 函數");
                    return false;
                }
            };

        let mut spec = Spec {
            Product: product,
            Capabilities: 0,
            SlotCount: 0,
        };

        let result = c_fn(&mut spec as *mut Spec);
        if result {
            let mut member_spec = m_spec.lock().unwrap();
            *member_spec = spec;
        }
        println!("Initial: result: {:?}, spec:{:?}", result, spec);
        return result;
    }
}


fn create_empty_battery_data() -> BatteryDataC {
    let mut rng = rand::thread_rng();
    BatteryDataC {
        ID: 0,
        SN: std::ptr::null(), // create_simulated_string(&format!("SIM{:08X}", rng.gen::<u32>())),
        Pack: 0,
        Mode: 0,
        DesignVoltage: 0,
        DesignCapacity: 0,
        RemainingCapacity: 0,
        FullyChargedCapacity: 0,
        RelativeStateOfCharged: 0,
        AbsoluteStateOfCharged: 0,
        UntilFullyCharged: 0,
        UntilFullyDischarged: 0,
        Cycle: 0,
        Voltage: 0,
        Current: 0,
        ChargingVoltage: 0,
        ChargingCurrent: 0,
        AverageCurrent: 0,
        Temperature: 0.0,
        ManufactureDate: std::ptr::null(),
        GaugeStatus: 0,
        Error: 0,
        /* Extended Data (XXL) */
        CycleIndex: 0,
        CycleThreshold: 0,
        FullyChargedDate: std::ptr::null(),
        FullyChargedCapacityThreshold: 0,
        FullyChargedCapacityBackup: 0,
        XXLLifetimeMaxPackVoltage: 0,
        XXLLifetimeMinPackVoltage: 0,
        XXLLifetimeMaxCurrent: 0,
        XXLLifetimeMinCurrent: 0,
        OrangeLED: 0,
        FullyChargedVoltage: 0,
        FirstUseDate: std::ptr::null(),
        RecordDate: std::ptr::null(),
        RecordTime: std::ptr::null(),
        PackMode: 0,
        DiagParamPfFlag: 0,
        /* Extended Data */
        CellVoltage_1: 0,
        CellVoltage_2: 0,
        CellVoltage_3: 0,
        CellVoltage_4: 0,
        PackVoltage: 0,
        FETControl: 0,
        SafetyAlert_1: 0,
        SafetyAlert_2: 0,
        SafetyStatus_1: 0,
        SafetyStatus_2: 0,
        PFAlert_1: 0,
        PFAlert_2: 0,
        PFStatus_1: 0,
        PFStatus_2: 0,
        OperationStatus: 0,
        ChargingStatus: 0,
        TemperatureRange: 0,
        MaxError: 0,
        DeviceName: std::ptr::null(),
        //MaxCellVoltage: 0,
        //MinCellVoltage: 0,
        /* Manufacture Block */
        ManufactureBlock_1: std::ptr::null(),
        ManufactureBlock_2: std::ptr::null(),
        ManufactureBlock_3: std::ptr::null(),
        ManufactureBlock_4: std::ptr::null(),
        /* Lifetime Data */
        LifetimeMaxTemperature: 0.0,
        LifetimeMinTemperature: 0.0,
        LifetimeAvgTemperature: 0.0,
        LifetimeMaxCellVoltage: 0,
        LifetimeMinCellVoltage: 0,
        LifetimeMaxPackVoltage: 0,
        LifetimeMinPackVoltage: 0,
        LifetimeMaxChargingCurrent: 0,
        LifetimeMaxDischargingCurrent: 0,
        LifetimeMaxAvgDischargingCurrent: 0,
        LifetimeMaxChargingPower: 0,
        LifetimeMaxDischargingPower: 0,
        LifetimeMaxAvgDischargingPower: 0,
        LifetimeTemperatureSamples: 0,
        OTEventCount: 0,
        OTEventDuration: 0,
        OVEventCount: 0,
        OVEventDuration: 0,
    }
}

// 創建模擬電池數據的函數
fn create_simulated_battery_data(slot_index: u8) -> BatteryDataC {
    let mut rng = rand::thread_rng();
    
    // 基於您提供的真實數據創建模擬數據
    let mut battery_data = create_empty_battery_data();
    
    // 設置基本資訊
    battery_data.ID = slot_index as u16;
    //battery_data.SN = CString::new(format!("H987654321-{:08}", slot_index + 1)).unwrap().into_raw();
    battery_data.SN = create_simulated_string(&format!("SIM{:08X}", rng.gen::<u32>()));
    battery_data.ManufactureDate = create_simulated_string(&format!("2024{:02}{:02}", rng.gen_range(1..13), rng.gen_range(1..29)));
    battery_data.FullyChargedDate = create_simulated_string(&format!("2024{:02}{:02}", rng.gen_range(1..13), rng.gen_range(1..29)));
    battery_data.FirstUseDate = create_simulated_string(&format!("2024{:02}{:02}", rng.gen_range(1..13), rng.gen_range(1..29)));
    battery_data.RecordDate = create_simulated_string(&format!("2024{:02}{:02}", rng.gen_range(1..13), rng.gen_range(1..29)));
    battery_data.RecordTime = create_simulated_string(&format!("{:02}:{:02}:{:02}", rng.gen_range(0..24), rng.gen_range(0..60), rng.gen_range(0..60)));
    battery_data.DeviceName = create_simulated_string(&format!("SimBattery{}", slot_index));
    battery_data.ManufactureBlock_1 = create_simulated_string("SimBlock1");
    battery_data.ManufactureBlock_2 = create_simulated_string("SimBlock2");
    battery_data.ManufactureBlock_3 = create_simulated_string("SimBlock3");
    battery_data.ManufactureBlock_4 = create_simulated_string("SimBlock4");
    battery_data.Pack = if rng.gen_bool(0.5) { 3 } else { 2 };
    battery_data.Mode = if rng.gen_bool(0.5) { 8192 } else { 24596 };
    battery_data.DesignVoltage = 10800;
    battery_data.DesignCapacity = if rng.gen_bool(0.5) { 11450 } else { 8550 };
    battery_data.RemainingCapacity = rng.gen_range(6000..11450);
    battery_data.FullyChargedCapacity = rng.gen_range(8000..11450);
    battery_data.RelativeStateOfCharged = rng.gen_range(20..100);
    battery_data.AbsoluteStateOfCharged = rng.gen_range(80..100);
    battery_data.UntilFullyCharged = 65535;
    battery_data.UntilFullyDischarged = 65535;
    battery_data.Cycle = rng.gen_range(1..1000);
    battery_data.Voltage = rng.gen_range(11000..12600);
    battery_data.Current = rng.gen_range(-100..100);
    battery_data.ChargingVoltage = rng.gen_range(12000..12600);
    battery_data.ChargingCurrent = rng.gen_range(0..4500);
    battery_data.AverageCurrent = rng.gen_range(-50..50);
    battery_data.Temperature = rng.gen_range(20.0..80.0);
    battery_data.GaugeStatus = rng.gen_range(96..256);
    battery_data.Error = if rng.gen_bool(0.1) { rng.gen_range(1..20000) } else { 0 };
    
    // 設置擴展數據
    battery_data.CycleThreshold = if rng.gen_bool(0.5) { 2000 } else { 0 };
    battery_data.FullyChargedCapacityThreshold = if rng.gen_bool(0.5) { 40 } else { 0 };
    battery_data.XXLLifetimeMaxPackVoltage = if rng.gen_bool(0.5) { 12450 } else { 0 };
    battery_data.XXLLifetimeMinPackVoltage = if rng.gen_bool(0.5) { 7776 } else { 0 };
    battery_data.XXLLifetimeMaxCurrent = if rng.gen_bool(0.5) { 3920 } else { 0 };
    battery_data.XXLLifetimeMinCurrent = if rng.gen_bool(0.5) { -9545 } else { 0 };
    battery_data.FullyChargedVoltage = if rng.gen_bool(0.5) { 4100 } else { 0 };
    battery_data.PackMode = if rng.gen_bool(0.5) { 43707 } else { 0 };
    
    // 設置電池電壓
    battery_data.CellVoltage_1 = rng.gen_range(3000..4200);
    battery_data.CellVoltage_2 = rng.gen_range(3800..4200);
    battery_data.CellVoltage_3 = rng.gen_range(3800..4200);
    battery_data.CellVoltage_4 = 0;
    battery_data.PackVoltage = rng.gen_range(4500..13700);
    battery_data.FETControl = rng.gen_range(0..8);
    battery_data.SafetyAlert_1 = rng.gen_range(0..65535);
    battery_data.SafetyAlert_2 = rng.gen_range(0..65535);
    battery_data.SafetyStatus_1 = rng.gen_range(0..65535);
    battery_data.SafetyStatus_2 = rng.gen_range(0..65535);
    battery_data.PFAlert_1 = rng.gen_range(0..65535);
    battery_data.PFAlert_2 = rng.gen_range(0..65535);
    battery_data.PFStatus_1 = rng.gen_range(0..65535);
    battery_data.PFStatus_2 = rng.gen_range(0..65535);

    // let voltages = [battery_data.CellVoltage_1, battery_data.CellVoltage_2, battery_data.CellVoltage_3];
    // let cell_voltage_max = *voltages.iter().max().unwrap();
    // let cell_voltage_min = *voltages.iter().min().unwrap();
    // battery_data.MaxCellVoltage = cell_voltage_max;
    // battery_data.MinCellVoltage = cell_voltage_min;

    
    // 設置狀態
    battery_data.OperationStatus = rng.gen_range(0..=65535);
    battery_data.ChargingStatus = rng.gen_range(0..1000);
    battery_data.TemperatureRange = 8;
    battery_data.MaxError = rng.gen_range(1..3);
    
    // 設置生命週期數據
    battery_data.LifetimeMaxTemperature = rng.gen_range(40.0..50.0);
    battery_data.LifetimeMinTemperature = rng.gen_range(5.0..15.0);
    battery_data.LifetimeAvgTemperature = rng.gen_range(20.0..25.0);
    battery_data.LifetimeMaxCellVoltage = rng.gen_range(4200..4220);
    battery_data.LifetimeMinCellVoltage = rng.gen_range(2500..3000);
    battery_data.LifetimeMaxPackVoltage = rng.gen_range(12600..12700);
    battery_data.LifetimeMinPackVoltage = rng.gen_range(8000..9000);
    battery_data.LifetimeMaxChargingCurrent = rng.gen_range(4000..4500);
    battery_data.LifetimeMaxDischargingCurrent = rng.gen_range(-9000..-4000);
    battery_data.LifetimeMaxAvgDischargingCurrent = rng.gen_range(-8500..-4000);
    battery_data.LifetimeMaxChargingPower = rng.gen_range(50000..55000);
    battery_data.LifetimeMaxDischargingPower = rng.gen_range(-105000..-50000);
    battery_data.LifetimeMaxAvgDischargingPower = rng.gen_range(-10000..-4000);
    battery_data.LifetimeTemperatureSamples = rng.gen_range(100000..700000);
    
    battery_data
}

// 更新模擬電池狀態（隨機插入/拔除）
fn update_simulation_batteries() {
    let mut rng = rand::thread_rng();
    let current_time = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let mut last_update = LAST_SIMULATION_UPDATE.lock().unwrap();
    
    // 每 3 秒更新一次電池狀態
    if current_time - *last_update < 3 {
        return;
    }
    
    *last_update = current_time;
    drop(last_update);
    
    let mut batteries = SIMULATED_BATTERIES.lock().unwrap();
    
    // 隨機改變電池插入狀態
    for i in 0..6 {
        let change_probability = 0.3; // 30% 機率改變狀態
        
        if rng.gen_bool(change_probability) {
            match &batteries[i] {
                Some(_) => {
                    // 電池已插入，有機會拔除
                    if rng.gen_bool(0.8) { // 40% 機率拔除
                        batteries[i] = None;
                        println!("模擬：電池 {} 被拔除", i);
                    } else {
                        // 更新電池數據 - 創建新的 SimulatedBatteryData
                        let sim_data = create_simulated_battery_data_struct(i as u8);
                        batteries[i] = Some(sim_data);
                        println!("模擬：電池 {} 數據更新", i);
                    }
                }
                None => {
                    // 電池未插入，有機會插入
                    if rng.gen_bool(0.6) { // 60% 機率插入
                        let sim_data = create_simulated_battery_data_struct(i as u8);
                        batteries[i] = Some(sim_data);
                        println!("模擬：電池 {} 被插入", i);
                    }
                }
            }
        }
    }
}

// 創建 SimulatedBatteryData 結構體
fn create_simulated_battery_data_struct(slot_index: u8) -> SimulatedBatteryData {
    let mut rng = rand::thread_rng();
    
    let cell_voltage_1: u16 = rng.gen_range(2800..4200);
    let cell_voltage_2: u16 = rng.gen_range(2800..4200);
    let cell_voltage_3: u16 = rng.gen_range(2800..4200);

    let voltages = [cell_voltage_1, cell_voltage_2, cell_voltage_3];

    let cell_voltage_max = *voltages.iter().max().unwrap();
    let cell_voltage_min = *voltages.iter().min().unwrap();
    SimulatedBatteryData {
        id: slot_index as u16,
        sn: format!("SIM{:08X}", rng.gen::<u32>()),
        pack: if rng.gen_bool(0.5) { 3 } else { 2 },
        mode: if rng.gen_bool(0.5) { 8192 } else { 24596 },
        design_voltage: 10800,
        design_capacity: if rng.gen_bool(0.5) { 11450 } else { 8550 },
        remaining_capacity: rng.gen_range(6000..11450),
        fully_charged_capacity: rng.gen_range(3000..11450),
        relative_state_of_charged: rng.gen_range(20..100),
        absolute_state_of_charged: rng.gen_range(80..100),
        until_fully_charged: 65535,
        until_fully_discharged: 65535,
        cycle: rng.gen_range(1..50),
        voltage: rng.gen_range(11000..12600),
        current: rng.gen_range(-100..100),
        charging_voltage: rng.gen_range(12000..12600),
        charging_current: rng.gen_range(0..4500),
        average_current: rng.gen_range(-50..50),
        temperature: rng.gen_range(20.0..30.0),
        manufacture_date: format!("2024{:02}{:02}", rng.gen_range(1..13), rng.gen_range(1..29)),
        gauge_status: rng.gen_range(96..256),
        error: if rng.gen_bool(0.1) { rng.gen_range(1..20000) } else { 0 },
        cycle_index: 0,
        cycle_threshold: if rng.gen_bool(0.5) { 2000 } else { 0 },
        fully_charged_date: format!("2024{:02}{:02}", rng.gen_range(1..13), rng.gen_range(1..29)),
        fully_charged_capacity_threshold: if rng.gen_bool(0.5) { 40 } else { 0 },
        fully_charged_capacity_backup: 0,
        xxl_lifetime_max_pack_voltage: if rng.gen_bool(0.5) { 12450 } else { 0 },
        xxl_lifetime_min_pack_voltage: if rng.gen_bool(0.5) { 7776 } else { 0 },
        xxl_lifetime_max_current: if rng.gen_bool(0.5) { 3920 } else { 0 },
        xxl_lifetime_min_current: if rng.gen_bool(0.5) { -9545 } else { 0 },
        orange_led: 0,
        fully_charged_voltage: if rng.gen_bool(0.5) { 4100 } else { 0 },
        first_use_date: format!("2024{:02}{:02}", rng.gen_range(1..13), rng.gen_range(1..29)),
        record_date: format!("2024{:02}{:02}", rng.gen_range(1..13), rng.gen_range(1..29)),
        record_time: format!("{:02}:{:02}:{:02}", rng.gen_range(0..24), rng.gen_range(0..60), rng.gen_range(0..60)),
        pack_mode: if rng.gen_bool(0.5) { 43707 } else { 0 },
        cell_voltage_1: cell_voltage_1,
        cell_voltage_2: cell_voltage_2,
        cell_voltage_3: cell_voltage_3,
        cell_voltage_4: 0,
        pack_voltage: rng.gen_range(12000..13700),
        fet_control: rng.gen_range(0..8),
        safety_alert_1: 0,
        safety_alert_2: 0,
        safety_status_1: 0,
        safety_status_2: 0,
        pf_alert_1: 0,
        pf_alert_2: 0,
        pf_status_1: 0,
        pf_status_2: 0,
        operation_status: rng.gen_range(0..50000),
        charging_status: rng.gen_range(0..1000),
        temperature_range: 8,
        max_error: rng.gen_range(1..3),
        max_cell_voltage: cell_voltage_max,
        min_cell_voltage: cell_voltage_min,
        device_name: format!("SimBattery{}", slot_index),
        manufacture_block_1: "SimBlock1".to_string(),
        manufacture_block_2: "SimBlock2".to_string(),
        manufacture_block_3: "SimBlock3".to_string(),
        manufacture_block_4: "SimBlock4".to_string(),
        lifetime_max_temperature: rng.gen_range(40.0..50.0),
        lifetime_min_temperature: rng.gen_range(5.0..15.0),
        lifetime_avg_temperature: rng.gen_range(20.0..25.0),
        lifetime_max_cell_voltage: rng.gen_range(4200..4220),
        lifetime_min_cell_voltage: rng.gen_range(2500..3000),
        lifetime_max_pack_voltage: rng.gen_range(12600..12700),
        lifetime_min_pack_voltage: rng.gen_range(8000..9000),
        lifetime_max_charging_current: rng.gen_range(4000..4500),
        lifetime_max_discharging_current: rng.gen_range(-9000..-4000),
        lifetime_max_avg_discharging_current: rng.gen_range(-8500..-4000),
        lifetime_max_charging_power: rng.gen_range(50000..55000),
        lifetime_max_discharging_power: rng.gen_range(-105000..-50000),
        lifetime_max_avg_discharging_power: rng.gen_range(-10000..-4000),
        lifetime_temperature_samples: rng.gen_range(100000..700000),
        ot_event_count: 0,
        ot_event_duration: 0,
        ov_event_count: 0,
        ov_event_duration: 0,
    }
}

// 啟用/停用模擬模式
#[tauri::command]
pub fn set_simulation_mode(enabled: bool) -> bool {
    let mut sim_mode = SIMULATION_MODE.lock().unwrap();
    *sim_mode = enabled;
    
    if enabled {
        println!("模擬模式已啟用");
        // 初始化一些電池數據
        let mut batteries = SIMULATED_BATTERIES.lock().unwrap();
        let mut rng = rand::thread_rng();
        
        // 清空現有數據
        for i in 0..6 {
            batteries[i] = None;
        }
        
        // 隨機插入 2-4 顆電池
        let initial_battery_count = rng.gen_range(2..5);
        for i in 0..initial_battery_count {
            batteries[i] = Some(create_simulated_battery_data_struct(i as u8));
            println!("模擬：初始化電池 {}", i);
        }
        
        // 重置更新時間
        let mut last_update = LAST_SIMULATION_UPDATE.lock().unwrap();
        *last_update = 0;
    } else {
        println!("模擬模式已停用");
        // 清空模擬電池數據
        let mut batteries = SIMULATED_BATTERIES.lock().unwrap();
        for i in 0..6 {
            batteries[i] = None;
        }
    }
    
    enabled
}

// 檢查是否為模擬模式
#[tauri::command]
pub fn is_simulation_mode() -> bool {
    *SIMULATION_MODE.lock().unwrap()
}

// 手動觸發模擬電池狀態更新
#[tauri::command]
pub fn trigger_simulation_update() -> bool {
    let is_simulation = *SIMULATION_MODE.lock().unwrap();
    if is_simulation {
        update_simulation_batteries();
        println!("手動觸發模擬電池狀態更新");
        true
    } else {
        println!("非模擬模式，無法觸發更新");
        false
    }
}

fn load_Battery_GetData(slot_index: u8) -> BatteryDataC {
    println!("load_battery_ID_function(slot_index:{}) )", slot_index);
    
    // 檢查 API 是否已載入
    let api_guard = match ONYX_API.lock() {
        Ok(guard) => guard,
        Err(e) => {
            eprintln!("無法取得 API mutex lock: {}", e);
            return create_empty_battery_data();
        }
    };
    
    if api_guard.is_none() {
        eprintln!("API 尚未載入");
        return create_empty_battery_data();
    }
    
    unsafe {
        let c_fn: Symbol<unsafe extern "C" fn(u8) -> BatteryDataC> =
            match api_guard.as_ref().unwrap().get(b"Battery_GetData") {
                Ok(f) => f,
                Err(e) => {
                    eprintln!("載入 Battery_GetData 失敗: {}", e);
                    return create_empty_battery_data();
                }
            };

        // 使用 std::panic::catch_unwind 來捕獲可能的 panic
        let result = std::panic::catch_unwind(|| {
            c_fn(slot_index)
        });
        
        match result {
            Ok(battery_data) => {
                println!("load_battery_ID_function(slot_index:{}, Battery ID: {}, Battery SN: {}) ) - 成功獲取電池資料", slot_index, battery_data.ID, c_str2String(battery_data.SN));
                battery_data
            }
            Err(_) => {
                eprintln!("呼叫 Battery_GetData 時發生 panic，slot_index: {}", slot_index);
                create_empty_battery_data()
            }
        }
    }
}

#[tauri::command]
pub fn Charger_GetBatteries() -> Vec<BatteryDataC> {
    // 檢查是否為模擬模式
    let is_simulation = *SIMULATION_MODE.lock().unwrap();
    
    if is_simulation {
        println!("使用模擬模式獲取電池數據");
        
        // 更新模擬電池狀態
        update_simulation_batteries();
        
        let mut result: Vec<BatteryDataC> = Vec::new();
        let batteries = SIMULATED_BATTERIES.lock().unwrap();
        
        // 轉換模擬數據為 BatteryDataC
        for (index, battery_opt) in batteries.iter().enumerate() {
            if let Some(sim_battery) = battery_opt {
                //let mut battery_data = create_empty_battery_data();
                
                let mut battery_data = create_simulated_battery_data(index as u8);
                // 將 SimulatedBatteryData 轉換為 BatteryDataC
                battery_data.ID = sim_battery.id;
                // battery_data.SN = CString::new(sim_battery.sn.clone()).unwrap().as_ptr();
                battery_data.SN = CString::new(format!("H987654321-{:08}", index + 1)).unwrap().into_raw();
                battery_data.Pack = sim_battery.pack;
                battery_data.Mode = sim_battery.mode;
                battery_data.DesignVoltage = sim_battery.design_voltage;
                battery_data.DesignCapacity = sim_battery.design_capacity;
                battery_data.RemainingCapacity = sim_battery.remaining_capacity;
                battery_data.FullyChargedCapacity = sim_battery.fully_charged_capacity;
                battery_data.RelativeStateOfCharged = sim_battery.relative_state_of_charged;
                battery_data.AbsoluteStateOfCharged = sim_battery.absolute_state_of_charged;
                battery_data.UntilFullyCharged = sim_battery.until_fully_charged;
                battery_data.UntilFullyDischarged = sim_battery.until_fully_discharged;
                battery_data.Cycle = sim_battery.cycle;
                battery_data.Voltage = sim_battery.voltage;
                battery_data.Current = sim_battery.current;
                battery_data.ChargingVoltage = sim_battery.charging_voltage;
                battery_data.ChargingCurrent = sim_battery.charging_current;
                battery_data.AverageCurrent = sim_battery.average_current;
                battery_data.Temperature = sim_battery.temperature;
                // battery_data.ManufactureDate = 0x187e4f1b6f0 as *const c_char;
                battery_data.GaugeStatus = sim_battery.gauge_status;
                battery_data.Error = sim_battery.error;
                battery_data.CycleIndex = sim_battery.cycle_index;
                battery_data.CycleThreshold = sim_battery.cycle_threshold;
                // battery_data.FullyChargedDate=0x187e4eec770 as *const c_char;
                battery_data.FullyChargedCapacityThreshold = sim_battery.fully_charged_capacity_threshold;
                battery_data.FullyChargedCapacityBackup = sim_battery.fully_charged_capacity_backup;
                battery_data.XXLLifetimeMaxPackVoltage = sim_battery.xxl_lifetime_max_pack_voltage;
                battery_data.XXLLifetimeMinPackVoltage = sim_battery.xxl_lifetime_min_pack_voltage;
                battery_data.XXLLifetimeMaxCurrent = sim_battery.xxl_lifetime_max_current;
                battery_data.XXLLifetimeMinCurrent = sim_battery.xxl_lifetime_min_current;
                battery_data.OrangeLED = sim_battery.orange_led;
                battery_data.FullyChargedVoltage = sim_battery.fully_charged_voltage;
                battery_data.DiagParamPfFlag = if (sim_battery.safety_status_1 & 0x20) > 0 { 1 } else { 0 };


                println!("safety_status_1: {} ",sim_battery.safety_status_1);
                println!("safety_status_1 & 0x20 = {}: ", sim_battery.safety_status_1 & 0x20);
                println!("DiagParamPfFlag = {}: ", battery_data.DiagParamPfFlag);

                // battery_data.FirstUseDate = 0x187e4eec780 as *const c_char;
                // battery_data.RecordDate = 0x187e4eec790 as *const c_char;
                // battery_data.RecordTime = 0x187e4eec7f0 as *const c_char;
                battery_data.PackMode = sim_battery.pack_mode;
                battery_data.CellVoltage_1 = sim_battery.cell_voltage_1;
                battery_data.CellVoltage_2 = sim_battery.cell_voltage_2;
                battery_data.CellVoltage_3 = sim_battery.cell_voltage_3;
                battery_data.CellVoltage_4 = sim_battery.cell_voltage_4;
                battery_data.PackVoltage = sim_battery.pack_voltage;
                battery_data.FETControl = sim_battery.fet_control;
                battery_data.SafetyAlert_1 = sim_battery.safety_alert_1;
                battery_data.SafetyAlert_2 = sim_battery.safety_alert_2;
                battery_data.SafetyStatus_1 = sim_battery.safety_status_1;
                battery_data.SafetyStatus_2 = sim_battery.safety_status_2;
                battery_data.PFAlert_1 = sim_battery.pf_alert_1;
                battery_data.PFAlert_2 = sim_battery.pf_alert_2;
                battery_data.PFStatus_1 = sim_battery.pf_status_1;
                battery_data.PFStatus_2 = sim_battery.pf_status_2;
                battery_data.OperationStatus = sim_battery.operation_status;
                battery_data.ChargingStatus = sim_battery.charging_status;
                battery_data.TemperatureRange = sim_battery.temperature_range;
                battery_data.MaxError = sim_battery.max_error;
                

                //let voltages = [sim_battery.cell_voltage_1, sim_battery.cell_voltage_2, sim_battery.cell_voltage_3];

                // let mut voltages: Vec<u16> = Vec::new();
                // // let voltages = [];
                // if sim_battery.cell_voltage_1 > 0 {
                //     voltages.push(sim_battery.cell_voltage_1);
                // }
                // if sim_battery.cell_voltage_2 > 0 {
                //     voltages.push(sim_battery.cell_voltage_2);
                // }
                // if sim_battery.cell_voltage_3 > 0 {
                //     voltages.push(sim_battery.cell_voltage_3);
                // }
                // if sim_battery.cell_voltage_4 > 0 {
                //     voltages.push(sim_battery.cell_voltage_4);
                // }
                // if voltages.is_empty() {
                //     // 如果沒有有效的電池電壓，則設置為 0
                //     battery_data.MaxCellVoltage = 0;
                //     battery_data.MinCellVoltage = 0;
                // }
                // let cell_voltage_max = *voltages.iter().max().unwrap();
                // let cell_voltage_min = *voltages.iter().min().unwrap();

                // battery_data.MaxCellVoltage = cell_voltage_max;
                // battery_data.MinCellVoltage = cell_voltage_min;

                // battery_data.DeviceName = 0x187e4eec720 as *const c_char;
                // battery_data.ManufactureBlock_1 = 0x187e4eec7a0 as *const c_char;
                // battery_data.ManufactureBlock_2 = 0x187e4eeca20 as *const c_char;
                // battery_data.ManufactureBlock_3 = 0x187e4f1b3b0 as *const c_char;
                // battery_data.ManufactureBlock_4 = 0x187e4f1b610 as *const c_char;
                battery_data.LifetimeMaxTemperature = sim_battery.lifetime_max_temperature;
                battery_data.LifetimeMinTemperature = sim_battery.lifetime_min_temperature;
                battery_data.LifetimeAvgTemperature = sim_battery.lifetime_avg_temperature;
                battery_data.LifetimeMaxCellVoltage = sim_battery.lifetime_max_cell_voltage;
                battery_data.LifetimeMinCellVoltage = sim_battery.lifetime_min_cell_voltage;
                battery_data.LifetimeMaxPackVoltage = sim_battery.lifetime_max_pack_voltage;
                battery_data.LifetimeMinPackVoltage = sim_battery.lifetime_min_pack_voltage;
                battery_data.LifetimeMaxChargingCurrent = sim_battery.lifetime_max_charging_current;
                battery_data.LifetimeMaxDischargingCurrent = sim_battery.lifetime_max_discharging_current;
                battery_data.LifetimeMaxAvgDischargingCurrent = sim_battery.lifetime_max_avg_discharging_current;
                battery_data.LifetimeMaxChargingPower = sim_battery.lifetime_max_charging_power;
                battery_data.LifetimeMaxDischargingPower = sim_battery.lifetime_max_discharging_power;
                battery_data.LifetimeMaxAvgDischargingPower = sim_battery.lifetime_max_avg_discharging_power;
                battery_data.LifetimeTemperatureSamples = sim_battery.lifetime_temperature_samples;
                battery_data.OTEventCount = sim_battery.ot_event_count;
                battery_data.OTEventDuration = sim_battery.ot_event_duration;
                battery_data.OVEventCount = sim_battery.ov_event_count;
                battery_data.OVEventDuration = sim_battery.ov_event_duration;
                
                println!("模擬電池 {}: ID={}, SN={}, 電量={}%",
                    index, battery_data.ID, sim_battery.sn, battery_data.RelativeStateOfCharged);
                
                result.push(battery_data);
            } else {
                // 插槽為空，添加空電池數據
                let empty_battery = create_empty_battery_data();
                result.push(empty_battery);
            }
        }
        
        println!("模擬模式：返回 {} 個電池數據", result.len());
        return result;
    }
    
    // 原始的真實設備模式
    // 使用 scope 來確保 mutex 及時釋放
    let slot_count = {
        let member_spec = m_spec.lock().unwrap();
        println!("member_spec: {:?}", member_spec);
        member_spec.SlotCount
    };
    
    // println!("Charger_GetBoardID: {}", Charger_GetBoardID());
    // match ConnectionStatus::try_from(Charger_GetAdapterStatus()) {
    //     Ok(status) => println!("Charger_GetAdapterStatus: {:?}", status),
    //     Err(_) => println!("Charger_GetAdapterStatus: Err(_)"),
    // }

    let mut linked_paths: Vec<BatteryDataC> = Vec::new();

    if slot_count > 0 {
        linked_paths = Vec::with_capacity(slot_count as usize);
        for slot_index in 0..slot_count {
            // let slot_status = match LockStatus::try_from(Charger_GetSlotStatus(slot_index)) {
            //     Ok(status) => {
            //         println!("Charger_GetSlotStatus({}): {:?}", slot_index, status);
            //         // if(status == "Normal") {
            //         //     LockStatus::Normal;
            //         // } else if(status == "Unlock")  {
            //         //     LockStatus::Unlock;
            //         // } else if(status == "Lock")  {
            //         //     LockStatus::Lock;
            //         // }
            //         // LockStatus::Normal;
            //         if status == LockStatus::Lock {
            //             // linked_paths.push(create_empty_battery_data());
            //             println!("插槽 {} 已鎖定，跳過獲取電池資料", slot_index);
            //             // continue;
            //         } 
            //         else if status == LockStatus::Unlock {
            //             println!("插槽 {} 已解鎖，跳過獲取電池資料", slot_index);
            //         } 
            //         else if status == LockStatus::None {
            //             println!("插槽 {} 狀態未知，跳過獲取電池資料", slot_index);
            //         }
            //         status
            //     },
            //     Err(_) => {
            //         // let err_msg = format!("Charger_GetSlotStatus({}) Err: {:?}", slot_index, e);
            //         eprintln!("Charger_GetSlotStatus({}): Err(_)", slot_index);
            //         // return Err("Charger_GetBatteries Err".to_string());
            //         //continue;
            //         LockStatus::None
            //     },
            // };
            
            // println!("slot_status: {:?}", slot_status);
            
            // 獲取電池資料
            println!("開始獲取 slot {} 的電池資料", slot_index);
            
            let battery_data: BatteryDataC = load_Battery_GetData(slot_index);
            // let battery_data: BatteryDataC = create_empty_battery_data();
            println!("完成獲取 slot {} 的電池資料", slot_index);

            println!("index: {}, battery_data: {:?}", slot_index, battery_data);
            // 取得 id 欄位
            println!("index: {}, Battery ID: {}, Battery SN: {}", slot_index, battery_data.ID, c_str2String(battery_data.SN));

            if c_str2String(battery_data.SN).is_empty() {
                println!("電池 SN 為空，跳過此電池");
                continue;
            }
            linked_paths.push(battery_data);
        }
    }
    
    println!("成功獲取 {} 個電池資料", linked_paths.len());
    
    linked_paths
}


ONYX_API!(String, Charger_GetName,);
ONYX_API!(String, Charger_GetVersion,);
ONYX_API!(String, Charger_GetSerialNumber,);
ONYX_API!(String, Charger_GetBoardID,);
ONYX_API!(u8, Charger_GetAdapterStatus,);
ONYX_API!(u8, Charger_GetSlotStatus, slot_index: u8);
ONYX_API!(u8, Charger_GetBatteryStatus, slot_index: u8);
ONYX_API!(u8, Charger_GetErrorCode,);
// ONYX_API!(bool, Charger_SetSerialNumber, plain_text: String);
ONYX_API!(u8, Charger_SetSlotStatus, slot_index: u8, lock_status: u8);
ONYX_API!(bool, Charger_SetReboot,);
ONYX_API!(bool, Charger_SetLED, switch_status: u8);
ONYX_API!(bool, Charger_SetBuzzer, switch_status: u8);
ONYX_API!(bool, Charger_SetSerialNumber, plain_text: *const c_char);

// #[tauri::command]
// pub fn Charger_SetSerialNumber(plain_text: String) -> bool {
//     unsafe {
//         let api = ONYX_API.lock().unwrap();
//         // let c_fn: Symbol<unsafe extern "C" fn(*const i8) -> bool> =
//         //     match api.as_ref().unwrap().get(b"Charger_SetSerialNumber") {
//         //         Ok(f) => f,
//         //         Err(_) => return false,
//         //     };

//         // let c_string = CString::new(plain_text).unwrap();
//         // let c_ptr = c_string.as_ptr();
//         // return c_fn(c_ptr);


//         let c_fn: Symbol<unsafe extern "C" fn(*const c_char) -> bool> =
//             match api.as_ref().unwrap().get(b"Charger_SetSerialNumber") {
//                 Ok(f) => f,
//                 Err(_) => return false,
//             };

        
//         let c_serial = CString::new(plain_text).unwrap();
//         return c_fn(c_serial.as_ptr());
        
//     }
// }

#[tauri::command]
pub fn Upgrade_SetUpgrade(file_path: String, b_force_upgrade: bool) -> bool {
    unsafe {
        let api = ONYX_API.lock().unwrap();
        let c_fn: Symbol<unsafe extern "C" fn(*const i8, bool) -> bool> =
            match api.as_ref().unwrap().get(b"Upgrade_SetUpgrade") {
                Ok(f) => f,
                Err(_) => return false,
            };

        let c_string = CString::new(file_path).unwrap();
        let c_ptr = c_string.as_ptr();
        return c_fn(c_ptr, b_force_upgrade);
    }
}

#[tauri::command]
pub fn Upgrade_SetProgressUpdated() {
    unsafe {
        let api = ONYX_API.lock().unwrap();
        let c_fn: Symbol<unsafe extern "C" fn(delegate: extern "C" fn(u8))> =
            match api.as_ref().unwrap().get(b"Upgrade_SetProgressUpdated") {
                Ok(f) => f,
                Err(_) => return,
            };

        return c_fn(ProgressUpdated);
    }
}

static mut APP_HANDLE: Option<Arc<Mutex<AppHandle>>> = None;

pub fn set_app_handle(handle: Arc<Mutex<AppHandle>>) {
    unsafe {
        APP_HANDLE = Some(handle);
    }
}

static CALLBACK: Lazy<Arc<Mutex<Option<Box<dyn Fn(u8) + Send + Sync>>>>> =
    Lazy::new(|| Arc::new(Mutex::new(None)));

extern "C" fn ProgressUpdated(nProgress: u8) {
    if let Some(cb) = CALLBACK.lock().unwrap().as_ref() {
        return cb(nProgress);
    }
}

#[tauri::command]
pub fn SetProgressUpdated(event_name: String) {
    let callback = move |nProgress: u8| {
        // println!("Received progress: {}", nProgress);
        unsafe {
            if let Some(app) = &APP_HANDLE {
                let locked = app.lock().unwrap();
                let _ = locked.emit(event_name.as_str(), nProgress);
            }
        }
    };
    *CALLBACK.lock().unwrap() = Some(Box::new(callback));
}

/*
    ONYX_API BatteryData Battery_GetData(unsigned char slot_index);

    ONYX_API unsigned char Network_GetWirelessMode();
    ONYX_API unsigned char Network_GetNetworkStatus();
    ONYX_API unsigned char Network_GetMQTTStatus();

    ONYX_API unsigned char Network_GetProperty(NetworkProperty &property);
    ONYX_API unsigned char Network_SetProperty(NetworkProperty &property);
    ONYX_API unsigned char Network_HostAP(NetworkProperty &property);
    ONYX_API unsigned char Network_SoftAP(NetworkProperty &property);
*/
