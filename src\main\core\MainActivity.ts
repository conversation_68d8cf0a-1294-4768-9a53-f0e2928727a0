// import { invoke } from "@tauri-apps/api/core";
// import { listen } from '@tauri-apps/api/event';
import { resolveResource } from '@tauri-apps/api/path';
// import { convertFileSrc } from '@tauri-apps/api/core';
import { readTextFile } from '@tauri-apps/plugin-fs';
// import { open, BaseDirectory } from '@tauri-apps/plugin-fs';
//import { open, readTextFileLines, BaseDirectory } from '@tauri-apps/plugin-fs';

import { get_batteries } from './scripts/pages/BatteryView';
import { BatteryLogViewController } from './scripts/pages/BatteryLogView';
import { upgrade } from './scripts/pages/ChargerDetailsView';
import { initSettings } from './scripts/pages/SettingsView';

import { get_batteries as get_diagnostics_batteries } from './scripts/pages/DiagnosticsView';

// 延遲函數
// async function delay(ms: number) {
//   return new Promise(resolve => setTimeout(resolve, ms));
// }

// 輔助函數：處理容器內所有圖片的路徑
async function processImagesInContainer(container: HTMLElement) {
  const images = container.querySelectorAll('img[src]') as NodeListOf<HTMLImageElement>;
  
  for (const img of images) {
    const originalSrc = img.getAttribute('src');
    if (originalSrc && !originalSrc.startsWith('data:') && !originalSrc.startsWith('http') && !originalSrc.startsWith('blob:')) {
      try {
        // 嘗試解析資源路徑
        let resourcePath = originalSrc;
        
        // 如果路徑不是以 src/ 開頭，嘗試添加前綴
        if (!resourcePath.startsWith('src/')) {
          // 處理相對路徑，如 "assets/image.png" -> "src/main/assets/image.png"
          if (resourcePath.startsWith('assets/')) {
            resourcePath = `src/main/${resourcePath}`;
          }
          // 處理其他相對路徑
          else if (!resourcePath.startsWith('/')) {
            resourcePath = `src/main/assets/${resourcePath}`;
          }
        }
        
        const resolvedPath = await resolveResource(resourcePath);
        const { convertFileSrc } = await import('@tauri-apps/api/core');
        const convertedSrc = convertFileSrc(resolvedPath);
        img.src = convertedSrc;
        
        console.log(`圖片路徑已轉換: ${originalSrc} -> ${convertedSrc}`);
      } catch (error) {
        console.error(`無法載入圖片 ${originalSrc}:`, error);
        // 可以設置一個預設圖片或隱藏圖片
        img.style.display = 'none';
      }
    }
  }
}

// 輔助函數：處理容器內所有腳本
async function processScriptsInContainer(container: HTMLElement) {
  const scripts = container.querySelectorAll('script[src]') as NodeListOf<HTMLScriptElement>;

  for (const script of scripts) {
    const originalSrc = script.getAttribute('src');
    if (originalSrc && !originalSrc.startsWith('data:') && !originalSrc.startsWith('http') && !originalSrc.startsWith('blob:')) {
      try {
        // 嘗試解析資源路徑
        let resourcePath = originalSrc;

        // 如果路徑不是以 src/ 開頭，嘗試添加前綴
        if (!resourcePath.startsWith('src/')) {
          // 處理相對路徑，如 "scripts/script.js" -> "src/main/scripts/script.js"
          if (resourcePath.startsWith('scripts/')) {
            resourcePath = `src/main/${resourcePath}`;
          }
          // 處理其他相對路徑
          else if (!resourcePath.startsWith('/')) {
            resourcePath = `src/main/scripts/${resourcePath}`;
          }
        }

        const resolvedPath = await resolveResource(resourcePath);
        const { convertFileSrc } = await import('@tauri-apps/api/core');
        const convertedSrc = convertFileSrc(resolvedPath);
        script.src = convertedSrc;

        console.log(`腳本路徑已轉換: ${originalSrc} -> ${convertedSrc}`);
      } catch (error) {
        console.error(`無法載入腳本 ${originalSrc}:`, error);
      }
    }
  }
}

// 輔助函數：處理容器內所有 CSS 背景圖片
async function processBackgroundImagesInContainer(container: HTMLElement) {
  // 處理所有具有 style 屬性的元素
  const elementsWithStyle = container.querySelectorAll('[style*="background"]') as NodeListOf<HTMLElement>;

  for (const element of elementsWithStyle) {
    const style = element.getAttribute('style');
    if (style) {
      console.log("style:" + style);
      const updatedStyle = await processBackgroundImageInStyle(style);
      console.log("updatedStyle:" + updatedStyle);
      if (updatedStyle !== style) {
        element.setAttribute('style', updatedStyle);
      }
    }
  }

  // 處理所有元素的計算樣式中的背景圖片
  const allElements = container.querySelectorAll('*') as NodeListOf<HTMLElement>;

  for (const element of allElements) {
    const computedStyle = window.getComputedStyle(element);
    const backgroundImage = computedStyle.backgroundImage;

    if (backgroundImage && backgroundImage !== 'none' && backgroundImage.includes('url(')) {
      const urlMatch = backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
      if (urlMatch && urlMatch[1]) {
        const originalUrl = urlMatch[1];

        if (!originalUrl.startsWith('data:') && !originalUrl.startsWith('http') && !originalUrl.startsWith('blob:')) {
          try {
            const convertedUrl = await convertImagePath(originalUrl);
            element.style.backgroundImage = convertedUrl;
            console.log(`背景圖片路徑已轉換: ${originalUrl} -> ${convertedUrl}`);
          } catch (error) {
            console.error(`無法載入背景圖片 ${originalUrl}:`, error);
          }
        }
      }
    }
  }
}

// 輔助函數：處理樣式字串中的背景圖片 URL
async function processBackgroundImageInStyle(styleString: string): Promise<string> {
  const urlRegex = /url\(['"]?([^'"]+)['"]?\)/g;
  let updatedStyle = styleString;
  let match;

  while ((match = urlRegex.exec(styleString)) !== null) {
    const originalUrl = match[1];

    if (!originalUrl.startsWith('data:') && !originalUrl.startsWith('http') && !originalUrl.startsWith('blob:')) {
      try {
        const convertedUrl = await convertImagePath(originalUrl);
        updatedStyle = updatedStyle.replace(match[0], `url('${convertedUrl}')`);
        console.log(`樣式中背景圖片路徑已轉換: ${originalUrl} -> ${convertedUrl}`);
      } catch (error) {
        console.error(`無法載入背景圖片 ${originalUrl}:`, error);
      }
    }
  }

  return updatedStyle;
}

// 輔助函數：轉換圖片路徑（與 processImagesInContainer 中的邏輯相同）
async function convertImagePath(originalPath: string): Promise<string> {
  let resourcePath = originalPath;

  // 移除開頭的斜線（如果有的話）
  if (resourcePath.startsWith('/')) {
    resourcePath = resourcePath.substring(1);
  }

  // 如果路徑不是以 src/ 開頭，嘗試添加前綴
  if (!resourcePath.startsWith('src/')) {
    // 處理相對路徑，如 "assets/image.png" -> "src/main/assets/image.png"
    if (resourcePath.startsWith('assets/')) {
      resourcePath = `src/main/${resourcePath}`;
    }
    // 處理其他相對路徑
    else {
      resourcePath = `src/main/assets/${resourcePath}`;
    }
  }

  const resolvedPath = await resolveResource(resourcePath);
  const { convertFileSrc } = await import('@tauri-apps/api/core');
  return convertFileSrc(resolvedPath);
}

// 輔助函數：首字母大寫
function capitalizeFirstLetter(text: string) {
  return text.charAt(0).toUpperCase() + text.slice(1);
}

// 定義主題事件的接口
interface ThemeChangeEvent extends CustomEvent<{ theme: string }> {
  detail: {
    theme: string;
  };
}

// 動態載入內容
window.addEventListener("DOMContentLoaded", async () => {
  try {
    // 獲取當前頁面
    const urlParams = new URLSearchParams(window.location.search);
    const page = urlParams.get('page') || 'view_battery';

    // 使用 asset:// 協議
    //const sidebarPath = 'asset://src/main/res/layout/components/sidebar.html';
    // 使用resolveResource獲取絕對路徑
    // const sidebarResourcePath = await resolveResource('src/main/res/layout/components/sidebar.html');
    // const sidebarPath = await convertFileSrc(sidebarResourcePath);
    
    //const headerResourcePath = await resolveResource('layout/components/header.html');

    //http://tauri.localhost/src/main/assets/128x128.png
    //http://tauri.localhost/src/main/assets/128x128.png

    // C:\Users\<USER>\AppData\Roaming\com.onyx.app\config.toml
    // try{
    //   const configToml = await readTextFile('config.toml', {
    //     baseDir: BaseDirectory.AppConfig,
    //   });
    //   alert("configToml:" + configToml);
    // }catch(error){
    //   alert("error:" + error);
    // }

    // //C:\Users\<USER>\AppData\Roaming\com.onyx.app\foo/bar.txt
    // try{
    //   const file = await open('foo/bar.txt', {
    //     read: true,
    //     baseDir: BaseDirectory.AppData,
    //   });
      
    //   const stat = await file.stat();
    //   const buf = new Uint8Array(stat.size);
    //   await file.read(buf);
    //   const textContents = new TextDecoder().decode(buf);
    //   await file.close();
  
    //   alert("textContents:" + textContents);
    // }catch(error){
    //   alert("error:" + error);
    // }


    // //C:\Users\<USER>\AppData\Local\com.onyx.app\logs\app.logs
    // try{
    //   const lines = await readTextFileLines('app.logs', {
    //     baseDir: BaseDirectory.AppLog,
    //   });
    //   for await (const line of lines) {
    //     console.log(line);
    //   }
    // }catch(error){
    //   alert("error:" + error);
    // }

    // //D:\0_project\RD\UPowerApplication\target\release\data\file.txt
    // try{
    //   const home = await readTextFile("data/file.txt", { 
    //     baseDir: BaseDirectory.Resource 
    //   });
    //   alert("home:" + home);
    // }catch(error){
    //   alert("error:" + error);
    // }

    // 載入側邊欄
    // 使用 readTextFile 直接讀取檔案內容，避免 CORS 問題
    try {
      const sidebarResourcePath = await resolveResource('src/main/res/layout/components/sidebar.html');
      //const sidebarPath = await convertFileSrc(sidebarResourcePath);
      //alert("sidebarPath:" + sidebarPath);
      const sidebarContent = await readTextFile(sidebarResourcePath);
      
      const sidebarContainer = document.getElementById('sidebar-container');
      if (sidebarContainer) {
        sidebarContainer.innerHTML = sidebarContent;

        // 處理 sidebar 中的所有圖片
        await processImagesInContainer(sidebarContainer);
        // 處理 sidebar 中的所有 CSS 背景圖片
        await processBackgroundImagesInContainer(sidebarContainer);
        // 抓出 script tag 並執行
        await processScriptsInContainer(sidebarContainer);

        // // 抓出 script tag 並執行
        // const scripts = sidebarContainer.querySelectorAll('script');
        // scripts.forEach((script) => {
        //   const newScript = document.createElement('script');
        //   if (script.src) {
        //     newScript.src = script.src;
        //   } else {
        //     newScript.textContent = script.textContent;
        //   }
        //   document.head.appendChild(newScript); // 或用 body 也可以
        //   //document.head.removeChild(newScript); // 可選：移除避免重複
        // });
      }
      
      // 設置當前頁面的選單項為活動狀態
      setTimeout(() => {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
          const href = item.getAttribute('href');
          if (href && href === `?page=${page}`) {
            item.classList.add('active');
          }
        });
      }, 100);
    } catch (error) {
      console.error("Error loading sidebar page:", error);
    }

    // fetch(sidebarResourcePath)
    //   .then(response => {
    //     if (!response.ok) {
    //       throw new Error(`側邊欄載入失敗: ${response.status}`);
    //     }
    //     return response.text();
    //   })
    //   .then(data => {
    //     const sidebarContainer = document.getElementById('sidebar-container');
    //     if (sidebarContainer) {
    //       sidebarContainer.innerHTML = data;
    //     }
        
    //     // 設置當前頁面的選單項為活動狀態
    //     setTimeout(() => {
    //       const menuItems = document.querySelectorAll('.menu-item');
    //       menuItems.forEach(item => {
    //         const href = item.getAttribute('href');
    //         if (href && href === `?page=${page}`) {
    //           item.classList.add('active');
    //         }
    //       });
    //     }, 100);
    //   })
    //   .catch(error => {
    //     alert("側邊欄載入錯誤:" + error);
    //     // 直接嵌入側邊欄HTML
    //     const sidebarContainer = document.getElementById('sidebar-container');
    //     if (sidebarContainer) {
    //       sidebarContainer.innerHTML = `
    //         <div class="sidebar">
    //           <div class="p-4 border-b border-gray-700">
    //             <div class="flex justify-center">
    //               <img src="../../assets/128x128.png" alt="Onyx Logo" class="h-12 w-12 mx-auto mt-4 md:mt-0">
    //             </div>
    //             <h1 class="text-center font-bold">
    //               <span class="text-accent-yellow">UPower</span>
    //             </h1>
    //           </div>
    //           <div class="mt-6">
    //             <a href="?page=view_battery" class="menu-item">
    //               <svg class="menu-icon" fill="currentColor" viewBox="0 0 20 20">
    //                 <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
    //               </svg>
    //               <span>Batteries</span>
    //             </a>
    //             <!-- 其他選單項... -->
    //           </div>
    //         </div>.
    //       `;

    //       // 設置當前頁面的選單項為活動狀態
    //       setTimeout(() => {
    //         const menuItems = document.querySelectorAll('.menu-item');
    //         menuItems.forEach(item => {
    //           const href = item.getAttribute('href');
    //           if (href && href === `?page=${page}`) {
    //             item.classList.add('active');
    //           }
    //         });
    //       }, 100);
    //     }
    //   });

    // 載入頂部欄
    try {
      const headerResourcePath = await resolveResource('src/main/res/layout/components/header.html');
      const headerContent = await readTextFile(headerResourcePath);
      
      const headerContainer = document.getElementById('header-container');
      if (headerContainer) {
        headerContainer.innerHTML = headerContent;
        // 處理 header 中的所有圖片
        await processImagesInContainer(headerContainer);
        // 處理 header 中的所有 CSS 背景圖片
        await processBackgroundImagesInContainer(headerContainer);
      }
      // 更新頁面標題
      setTimeout(() => {
        const pageTitle = document.getElementById('page-title');
        // pageConfig.ts 全局變數，載入頁面對應 Script 及 Title
        if ((window as any).pageConfig && (window as any).pageConfig[page]?.title) {
          if (pageTitle) {
            pageTitle.textContent = (window as any).pageConfig[page].title;
          }
        }
        else{
          // pageConfig.ts 內若沒有設定的頁面，使用檔名去掉前綴詞 view_ 後的名稱
          if (pageTitle) {
            pageTitle.textContent = '';
            pageTitle.textContent = capitalizeFirstLetter((page || '').replace(/^view_/, ''));
          }
        }
        
      }, 100);
    } catch (error) {
      console.error('Error loading header page:', error);
    }
    ///

    // const headerResourcePath = await resolveResource('src/main/res/layout/components/header.html');
    // const headerPath = await convertFileSrc(headerResourcePath);
    
    // console.log("嘗試載入頂部欄:", headerPath);
    
    // fetch('components/header.html')
    //   .then(response => response.text())
    //   .then(data => {
    //     const headerContainer = document.getElementById('header-container');
    //     if (headerContainer) {
    //       headerContainer.innerHTML = data;
    //     }

    //     // 更新頁面標題
    //     setTimeout(() => {
    //       const pageTitle = document.getElementById('page-title');
    //       // pageConfig.ts 全局變數，載入頁面對應 Script 及 Title
    //       if ((window as any).pageConfig && (window as any).pageConfig[page]?.title) {
    //         if (pageTitle) {
    //           pageTitle.textContent = (window as any).pageConfig[page].title;
    //         }
    //       }
    //       else{
    //         // pageConfig.ts 內若沒有設定的頁面，使用檔名去掉前綴詞 view_ 後的名稱
    //         if (pageTitle) {
    //           pageTitle.textContent = '';
    //           pageTitle.textContent = capitalizeFirstLetter((page || '').replace(/^view_/, ''));
    //         }
    //       }
          
    //     }, 100);
    //   });

    // 載入頁面內容
    try {
      const pageResourcePath = await resolveResource(`src/main/res/layout/pages/${page}.html`);
      const pageContent = await readTextFile(pageResourcePath);
      
      const contentContainer = document.getElementById('content-container');
      if (contentContainer) {
        contentContainer.innerHTML = pageContent;
        contentContainer.classList.add('fade-in');
        
        // 抓出 script tag 並執行
        const scripts = contentContainer.querySelectorAll('script');
        scripts.forEach((script) => {
          const newScript = document.createElement('script');
          if (script.src && script.src !== "") {
            console.log('script.src: ' + script.src);
            newScript.src = script.src;
          } else {
            //console.log('script.textContent: ' + script.textContent);
            newScript.textContent = script.textContent;
          }
          document.head.appendChild(newScript); // 或用 body 也可以
          //document.head.removeChild(newScript); // 可選：移除避免重複
        });

        // 處理 content 中的所有 JS script tag
        //await processScriptsInContainer(contentContainer);

        // 處理 content 中的所有圖片
        await processImagesInContainer(contentContainer);
        // 處理 content 中的所有 CSS 背景圖片
        await processBackgroundImagesInContainer(contentContainer);
        
        // pageConfig.ts 全局變數，載入頁面對應 Script 及 Title
        if ((window as any).pageConfig && (window as any).pageConfig[page]?.scriptPath) {
          const script = document.createElement('script');
          script.type = "module"; // 添加這行，將腳本標記為模組
          script.src = (window as any).pageConfig[page].scriptPath;
          document.body.appendChild(script);
        }

        if(page == "view_battery"){
          //console.log("get_productinfo()");
          await get_batteries();

          //console.log("BatteryViewController 初始化");
          //const controller = new BatteryViewController();
          //controller.initialize();
        }
        else if(page == "view_batteryLog"){
          console.log("BatteryViewController 初始化");
          const batteryLogController = new BatteryLogViewController();
          batteryLogController.initialize();
        }
        else if(page == "view_chargerDetails"){
          upgrade();
          // const controller = new BatteryViewController();
          // controller.upgrade();
        }
        else if(page == "view_settings"){
          initSettings();
        }
        else if(page == "view_diagnostics"){
          await get_diagnostics_batteries();
        }
      }
    } catch (error) {
      console.error('Error loading page:', error);
      const contentContainer = document.getElementById('content-container');
      if (contentContainer) {
        contentContainer.innerHTML = `
          <div class="card p-6 text-center">
          "${error}"
            <h2 class="text-xl font-bold mb-4">Page Not Found</h2>
            <p class="text-gray-400">The requested page "${page}" does not exist or cannot be loaded.</p>
            <a href="?page=dashboard" class="mt-4 inline-block px-4 py-2 bg-accent-yellow text-black rounded">Back to Home</a>
          </div>
        `;
      }
    }
    ///
    // const pageResourcePath = await resolveResource(`src/main/res/layout/pages/${page}.html`);
    // const pagePath = await convertFileSrc(pageResourcePath);
    
    // console.log("嘗試載入頁面:", pagePath);
    
    // fetch(pagePath)
    //   .then(response => {
    //     if (!response.ok) {
    //       throw new Error('Page not found');
    //     }
    //     return response.text();
    //   })
    //   .then(data => {
    //     const contentContainer = document.getElementById('content-container');
    //     if (contentContainer) {
    //       contentContainer.innerHTML = data;
    //       contentContainer.classList.add('fade-in');

    //       // pageConfig.ts 全局變數，載入頁面對應 Script 及 Title
    //       if ((window as any).pageConfig && (window as any).pageConfig[page]?.scriptPath) {
    //         const script = document.createElement('script');
    //         script.type = "module"; // 添加這行，將腳本標記為模組
    //         script.src = (window as any).pageConfig[page].scriptPath;
    //         document.body.appendChild(script);
    //       }
    //     }
    //   })
    //   .catch(error => {
    //     console.error('Error loading page:', error);
    //     const contentContainer = document.getElementById('content-container');
    //     if (contentContainer) {
    //       contentContainer.innerHTML = `
    //         <div class="card p-6 text-center">
    //         "${error.message}"
    //           <h2 class="text-xl font-bold mb-4">Page Not Found</h2>
    //           <p class="text-gray-400">The requested page "${page}" does not exist or cannot be loaded.</p>
    //           <a href="?page=dashboard" class="mt-4 inline-block px-4 py-2 bg-accent-yellow text-black rounded">Back to Home</a>
    //         </div>
    //       `;
    //     }
    //   });

    // 載入頁腳
    try {
      // 載入頁腳
      const footerResourcePath = await resolveResource('src/main/res/layout/components/footer.html');
      const footerContent = await readTextFile(footerResourcePath);
      
      const footerContainer = document.getElementById('footer-container');
      if (footerContainer) {
        footerContainer.innerHTML = footerContent;
        // 處理 footer 中的所有圖片
        await processImagesInContainer(footerContainer);
        // 處理 footer 中的所有 CSS 背景圖片
        await processBackgroundImagesInContainer(footerContainer);
      }
    } catch (error) {
      console.error("Error loading footer page:", error);
    }
    
    //const footerResourcePath = await resolveResource('src/main/res/layout/components/footer.html');
    //const footerPath = await convertFileSrc(footerResourcePath);
    
    //console.log("嘗試載入頁腳:", footerPath);
    
    // fetch('components/footer.html')
    //   .then(response => response.text())
    //   .then(data => {
    //     // alert(data);
    //     const footerContainer = document.getElementById('footer-container');
    //     if (footerContainer) {
    //       footerContainer.innerHTML = data;
    //     }
    //   });
  } catch (error) {
    console.error("資源載入過程中發生錯誤:", error);
  }


});

// 全局主題初始化
window.addEventListener('DOMContentLoaded', () => {
  const currentTheme = localStorage.getItem('theme') || 'light';

  // 監聽主題變更事件，使用類型斷言
  document.addEventListener('themeChange', ((e: ThemeChangeEvent) => {
    const newTheme = e.detail.theme;
    if (newTheme === 'light') {
      document.body.classList.remove('dark-mode');
      document.body.classList.add('light-mode');
      document.documentElement.style.setProperty('--bg-color', '#f8f9fa');
      document.documentElement.style.setProperty('--text-color', '#333333');
    } else {
      document.body.classList.remove('light-mode');
      document.body.classList.add('dark-mode');
      document.documentElement.style.setProperty('--bg-color', '#1E2126');
      document.documentElement.style.setProperty('--text-color', '#f6f6f6');
    }
  }) as EventListener);

  // 觸發全局主題變更事件，讓其他頁面也能響應
  const themeChangeEvent = new CustomEvent('themeChange', { detail: { theme: currentTheme } });
  document.dispatchEvent(themeChangeEvent);
});
