#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use chrono::Utc;
use rusb::Context;
use rusb::UsbContext;
use std::sync::Arc;
use std::sync::Mutex;
use std::thread;
use std::time::Duration;
use tauri::Emitter;
use tauri::Manager;

mod module;

fn monitor_usb_events(
    app_handle: Arc<Mutex<tauri::AppHandle>>,
) -> Result<(), Box<dyn std::error::Error>> {
    let context = Context::new()?;
    let mut last_devices = vec![];

    loop {
        let devices = context.devices()?.iter().collect::<Vec<_>>();

        if devices.len() != last_devices.len() {
            let app_handle = app_handle.lock().unwrap();
            app_handle.emit(
                "USB_ChangedEvent",
                format!("USB devices changed: {}", devices.len()),
            )?;
        }

        last_devices = devices;
        thread::sleep(Duration::from_secs(1));
    }
}

fn main() {
    // 設定更詳細的日誌
    // env_logger::init();
    
    // 印出應用程式啟動資訊
    println!("應用程式啟動中...");
    println!("工作目錄: {:?}", std::env::current_dir().unwrap_or_default());
    println!("啟動時間: {}", Utc::now().format("%Y-%m-%d %H:%M:%S"));
    // 添加 panic hook 來捕獲崩潰資訊
    std::panic::set_hook(Box::new(|panic_info| {
        let error_msg = format!("應用程式 panic: {:?}", panic_info);
        eprintln!("{}", error_msg);


        // 同時寫入多個位置
        let log_paths = vec![
            "crash_log.txt",
            "debug_log.txt",
        ];

        // 寫入日誌檔案
        for path in log_paths {
            if let Ok(mut file) = std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(path) {
                use std::io::Write;
                writeln!(file, "[{}] {}", 
                    chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"), 
                    error_msg).ok();
            }
        }
        
    }));
    
    println!("開始執行應用程式...");
    run()
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_http::init())  // HTTP 插件
        .plugin(tauri_plugin_fs::init()) // 文件系統插件
        .invoke_handler(tauri::generate_handler![
            module::UPowerSDK::LOAD_SDK,
            module::UPowerSDK::TEST,
            module::UPowerSDK::TestLed,
            module::UPowerSDK::TestBuzzer,
            module::UPowerSDK::Charger_GetBatteries,
            module::UPowerSDK::Initial,
            //module::UPowerSDK::load_Battery_GetData,
            module::UPowerSDK::Charger_GetName,
            module::UPowerSDK::Charger_GetVersion,
            module::UPowerSDK::Charger_GetSerialNumber,
            module::UPowerSDK::Charger_GetBoardID,
            module::UPowerSDK::Charger_GetAdapterStatus,
            module::UPowerSDK::Charger_GetSlotStatus,
            module::UPowerSDK::Charger_GetBatteryStatus,
            module::UPowerSDK::Charger_GetErrorCode,
            //module::UPowerSDK::Charger_SetSerialNumber,
            module::UPowerSDK::Charger_SetSlotStatus,
            module::UPowerSDK::Charger_SetReboot,
            module::UPowerSDK::Charger_SetLED,
            module::UPowerSDK::Charger_SetBuzzer,
            module::UPowerSDK::TestLed,
            module::UPowerSDK::TestBuzzer,
            module::UPowerSDK::Upgrade_SetUpgrade,
            module::UPowerSDK::Upgrade_SetProgressUpdated,
        ])
        .setup(|app| {
            let app_handle = Arc::new(Mutex::new(app.app_handle().clone()));
            _ = module::UPowerSDK::set_app_handle(Arc::clone(&app_handle));
            std::thread::spawn(move || {
                if let Err(e) = monitor_usb_events(Arc::clone(&app_handle)) {
                    eprintln!("Error monitoring USB events: {}", e);
                }
            });
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
